'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { LoginSchema, LoginData } from '@mtbrmg/shared';
import { useAuthStore } from '@/lib/stores/auth-store';
import { DEMO_FOUNDER_CREDS } from '@/lib/demo-data';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Eye, EyeOff } from 'lucide-react';
import Image from 'next/image';

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, error, clearError } = useAuthStore();
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<LoginData>({
    resolver: zodResolver(LoginSchema),
  });

  const onSubmit = async (data: LoginData) => {
    try {
      clearError();

      // Convert email to username for API login (our API expects username)
      let username = data.email;
      if (data.email === DEMO_FOUNDER_CREDS.email) {
        username = 'founder';
      } else if (data.email === '<EMAIL>') {
        username = 'admin';
      }

      const loginData = {
        username: username,
        password: data.password
      };

      // Try actual API login
      await login(loginData as any);

      // Redirect based on user role or email
      if (data.email === DEMO_FOUNDER_CREDS.email) {
        router.push('/founder-dashboard');
      } else {
        router.push('/dashboard');
      }
    } catch (error) {
      // Error is handled by the store
    }
  };

  const handleDemoLogin = () => {
    // Auto-fill demo credentials
    setValue('email', DEMO_FOUNDER_CREDS.email);
    setValue('password', DEMO_FOUNDER_CREDS.password);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-brand-primary via-brand-secondary to-brand-accent p-4" dir="rtl">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Image
              src="/the_logo.png"
              alt="MTBRMG ERP"
              width={80}
              height={80}
              className="rounded-lg"
            />
          </div>
          <CardTitle className="text-2xl font-bold text-brand-primary">
            نظام إدارة MTBRMG
          </CardTitle>
          <CardDescription>
            قم بتسجيل الدخول للوصول إلى لوحة التحكم
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email">البريد الإلكتروني</Label>
              <Input
                id="email"
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                {...register('email')}
                className={errors.email ? 'border-red-500' : ''}
                dir="ltr"
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">كلمة المرور</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="أدخل كلمة المرور"
                  {...register('password')}
                  className={errors.password ? 'border-red-500' : ''}
                  dir="ltr"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-500">{errors.password.message}</p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full bg-brand-primary hover:bg-brand-secondary"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري تسجيل الدخول...
                </>
              ) : (
                'تسجيل الدخول'
              )}
            </Button>

            <Button
              type="button"
              variant="outline"
              className="w-full mt-2"
              onClick={handleDemoLogin}
            >
              تجربة النظام (تسجيل دخول تجريبي)
            </Button>
          </form>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800 mb-2">بيانات الدخول المتاحة:</h3>
            <div className="text-xs text-blue-700 space-y-2">
              <div>
                <p className="font-medium">حساب المؤسس:</p>
                <p><strong>البريد الإلكتروني:</strong> {DEMO_FOUNDER_CREDS.email}</p>
                <p><strong>كلمة المرور:</strong> {DEMO_FOUNDER_CREDS.password}</p>
              </div>
              <p className="text-blue-600 mt-2">اضغط على "تجربة النظام" لملء بيانات المؤسس تلقائياً</p>
            </div>
          </div>

          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              لا تملك حساب؟{' '}
              <Button
                variant="link"
                className="p-0 h-auto text-brand-primary hover:text-brand-secondary"
                onClick={() => router.push('/register')}
              >
                إنشاء حساب جديد
              </Button>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
